{"endpoint": "a3i72wo3tqo4n3-ats.iot.eu-central-1.amazonaws.com", "cert": "/home/<USER>/claim/certificate.pem", "key": "/home/<USER>/claim/private.key", "root-ca": "/home/<USER>/CA/root-CA.crt", "thing-name": "uvionix-drone-real", "logging": {"level": "DEBUG", "type": "STDOUT", "file": "/var/log/aws-iot-device-client/aws-iot-device-client.log", "enable-sdk-logging": false, "sdk-log-level": "TRACE", "sdk-log-file": "/var/log/aws-iot-device-client/sdk.log"}, "jobs": {"enabled": false, "handler-directory": "/home/<USER>/.aws-iot-device-client/jobs"}, "tunneling": {"enabled": true}, "device-defender": {"enabled": false, "interval": 300}, "fleet-provisioning": {"enabled": true, "template-name": "uvionix_dev_ProvisoningTemplate", "template-parameters": "{\"ThingName\": \"uvionix-drone-real\", \"Serial\": \"777\"}", "csr-file": "", "device-key": ""}, "samples": {"pub-sub": {"enabled": false, "publish-topic": "", "publish-file": "/home/<USER>/.aws-iot-device-client/pubsub/publish-file.txt", "subscribe-topic": "", "subscribe-file": "/home/<USER>/.aws-iot-device-client/pubsub/subscribe-file.txt"}}, "config-shadow": {"enabled": false}, "sample-shadow": {"enabled": false, "shadow-name": "", "shadow-input-file": "", "shadow-output-file": ""}}