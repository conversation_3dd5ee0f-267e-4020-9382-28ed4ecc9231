{"cert": "/home/<USER>/claim/certificate.pem", "key": "/home/<USER>/claim/private.key", "root-ca": "/home/<USER>/CA/root-CA.crt", "thing-name": "{{thing_name}}-client", "endpoint": "{{iot_endpoint}}", "logging": {"level": "INFO", "type": "STDOUT", "file": "/var/log/aws-iot-device-client/aws-iot-device-client.log", "enable-sdk-logging": true, "sdk-log-level": "INFO", "sdk-log-file": "/var/log/aws-iot-device-client/sdk.log"}, "tunneling": {"enabled": true}, "fleet-provisioning": {"enabled": true, "template-name": "{{template_name}}", "template-parameters": "{\"ThingName\": \"{{thing_name}}\", \"Serial\": \"{{thing_serial}}\"}", "csr-file": "", "device-key": ""}}