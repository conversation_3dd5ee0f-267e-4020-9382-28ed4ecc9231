/**
 * Chart Manager Class
 * Handles Chart.js configuration and updates with proper decimation
 */
class ChartManager {
    constructor(canvasId, dataManager, colorManager, debug = true) {
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        this.dataManager = dataManager;
        this.colorManager = colorManager;
        this.timeWindowSeconds = 30;
        this.spanGapsValue = 3000;
        this.needsUpdate = false;
        this.lastUpdateTime = 0;
        this.updateThrottle = 8; // ~120fps for high-frequency data
        this.debug = debug;

        // Show all data points - no decimation or performance limits

        // Time window tracking for better update detection
        this.lastStartTime = null;
        this.lastEndTime = null;

        this.chart = new Chart(this.ctx, this.getChartConfig());

        // Throttled update function with performance monitoring
        this.throttledUpdate = this.throttle(() => {
            if (this.needsUpdate) {
                try {
                    const startTime = performance.now();
                    this.chart.update('none'); // Use 'none' mode for fastest updates
                    const updateTime = performance.now() - startTime;

                    // Adaptive performance adjustment
                    if (updateTime > 16) { // More than one frame (60fps)
                        this.adjustPerformanceSettings(updateTime);
                    }

                    this.needsUpdate = false;
                } catch (error) {
                    console.error('Chart update error:', error);
                    if (this.debug) {
                        console.log('Chart datasets:', this.chart.data.datasets);
                    }
                }
            }
        }, this.updateThrottle);
    }

    adjustPerformanceSettings(updateTime) {
        // No performance adjustments - show all data points
        if (this.debug) {
            const activeDatasets = this.chart.data.datasets.length;
            console.log(`Update time: ${updateTime.toFixed(1)}ms, Datasets: ${activeDatasets}, Showing all data points`);
        }
    }
    
    getChartConfig() {
        return {
            type: 'line',
            data: {
                datasets: []
            },
            options: {
                parsing: false,
                responsive: true,
                maintainAspectRatio: false,
                normalized: true,

                // Ensure all data points are processed
                datasets: {
                    line: {
                        pointRadius: 1,
                        showLine: true
                    }
                },
                
                elements: {
                    point: {
                        radius: 1,
                        hoverRadius: 4,
                        hitRadius: 8
                    },
                    line: { 
                        tension: 0,
                        borderWidth: 1.5
                    }
                },
                
                layout: {
                    padding: { top: 10, right: 10, bottom: 10, left: 10 }
                },
                
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: (context) => context.tick.value === 0 ? 'rgba(0, 0, 0, 0.1)' : 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            color: () => document.documentElement.getAttribute('data-bs-theme') === 'dark' ? '#e9ecef' : '#666'
                        }
                    },
                    x: {
                        type: 'time',
                        time: {
                            unit: 'minute',
                            displayFormats: { minute: 'HH:mm' },
                            minUnit: 'millisecond' // Allow millisecond precision
                        },
                        title: {
                            display: true,
                            text: 'Time',
                            color: () => document.documentElement.getAttribute('data-bs-theme') === 'dark' ? '#e9ecef' : '#666'
                        },
                        grid: {
                            color: () => document.documentElement.getAttribute('data-bs-theme') === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            color: () => document.documentElement.getAttribute('data-bs-theme') === 'dark' ? '#e9ecef' : '#666',
                            maxTicksLimit: 10,
                            autoSkip: false, // Don't automatically skip ticks
                            maxRotation: 0,
                            minRotation: 0
                        },
                        // Ensure no automatic data sampling
                        bounds: 'data',
                        adapters: {
                            date: {} // Use default date adapter without sampling
                        }
                    }
                },
                
                animation: false,
                
                plugins: {
                    decimation: {
                        enabled: false // We'll handle decimation manually for better control
                    },
                    legend: { display: false },
                    tooltip: {
                        enabled: true,
                        position: "nearest",
                        mode: 'index',
                        intersect: false,
                        animation: false,
                        filter: function(tooltipItem) {
                            // Limit tooltip items for performance
                            return tooltipItem.datasetIndex < 10;
                        },
                        callbacks: {
                            label: (context) => {
                                const value = typeof context.parsed.y === 'number' ?
                                    context.parsed.y.toFixed(3) : context.parsed.y;
                                return `${context.dataset.label}: ${value}`;
                            }
                        }
                    }
                },
                
                interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false
                },

                // Performance settings to ensure all data points are rendered
                performance: {
                    // Disable any automatic optimizations that might skip data points
                    animationDuration: 0
                }
            }
        };
    }
    
    updateTimeWindow(startTime, endTime) {
        this.chart.options.scales.x.min = startTime;
        this.chart.options.scales.x.max = endTime;
        this.updateTimeAxisDisplay();
        this.requestUpdate();
    }
    
    updateTimeAxisDisplay() {
        const timeRange = this.timeWindowSeconds;

        if (timeRange <= 5) {
            // For very short time windows, show milliseconds
            this.chart.options.scales.x.time.unit = 'millisecond';
            this.chart.options.scales.x.time.displayFormats = { millisecond: 'HH:mm:ss.SSS' };
            this.chart.options.scales.x.ticks.maxTicksLimit = 10;
        } else if (timeRange <= 30) {
            this.chart.options.scales.x.time.unit = 'second';
            this.chart.options.scales.x.time.displayFormats = { second: 'HH:mm:ss' };
            this.chart.options.scales.x.ticks.maxTicksLimit = 15;
        } else if (timeRange <= 300) {
            this.chart.options.scales.x.time.unit = 'second';
            this.chart.options.scales.x.time.displayFormats = { second: 'HH:mm:ss' };
            this.chart.options.scales.x.ticks.maxTicksLimit = 20;
        } else {
            this.chart.options.scales.x.time.unit = 'minute';
            this.chart.options.scales.x.time.displayFormats = { minute: 'HH:mm' };
            this.chart.options.scales.x.ticks.maxTicksLimit = 15;
        }
    }
    
    updateDatasets() {
        const activeMetrics = this.dataManager.getActiveMetrics();
        const currentDatasets = new Set(this.chart.data.datasets.map(ds => ds.label));
        
        // Remove inactive datasets
        this.chart.data.datasets = this.chart.data.datasets.filter(dataset => 
            activeMetrics.includes(dataset.label)
        );
        
        // Add new datasets
        activeMetrics.forEach(metric => {
            if (!currentDatasets.has(metric)) {
                const color = this.colorManager.getMetricColor(metric);
                this.chart.data.datasets.push({
                    label: metric,
                    data: [],
                    borderColor: color,
                    backgroundColor: color + '20', // Add transparency
                    tension: 0,
                    fill: false,
                    spanGaps: this.spanGapsValue,
                    parsing: false,
                    pointRadius: 1, // Make points visible
                    pointHoverRadius: 4,
                    pointHitRadius: 8,
                    borderWidth: 1.5,
                    showLine: true, // Ensure lines are drawn between all points
                    stepped: false // Don't step between points
                });
            }
        });
        
        this.requestUpdate();
    }
    
    updateVisibleData(startTime, endTime) {
        // Update timestamp tracking
        this.lastStartTime = startTime?.getTime();
        this.lastEndTime = endTime?.getTime();

        this.chart.data.datasets.forEach((dataset) => {
            const metricName = dataset.label;
            const rangeData = this.dataManager.getDataInRange(metricName, startTime, endTime);

            // Always update to ensure all data points appear - no caching optimization



            // Ensure data is properly formatted and sorted
            const formattedData = rangeData
                .filter(point => {
                    return point &&
                           point.x &&
                           point.y !== undefined &&
                           point.y !== null &&
                           !isNaN(point.y) &&
                           point.x instanceof Date &&
                           !isNaN(point.x.getTime());
                })
                .sort((a, b) => a.x.getTime() - b.x.getTime())
                .map(point => ({
                    x: point.x.getTime(), // Use timestamp for Chart.js
                    y: Number(point.y)
                }));

            // Show all data points - no decimation
            dataset.data = formattedData;

            // Debug: Log data point timestamps to see actual spacing
            if (this.debug && formattedData.length > 1) {
                const recentData = formattedData.slice(-10); // Last 10 points
                const timestamps = recentData.map(p => new Date(p.x).toISOString());
                console.log(`${metricName} recent timestamps:`, timestamps);
                const intervals = [];
                for (let i = 1; i < recentData.length; i++) {
                    intervals.push(recentData[i].x - recentData[i-1].x);
                }
                console.log(`${metricName} recent intervals (ms):`, intervals);
                console.log(`${metricName} total points in dataset:`, formattedData.length);
            }

            // Debug logging for problematic datasets
            if (this.debug && dataset.data.some(point => !point || point.x === undefined || point.y === undefined)) {
                console.warn(`Invalid data points in dataset ${metricName}:`,
                    dataset.data.filter(point => !point || point.x === undefined || point.y === undefined));
                // Clean up invalid points
                dataset.data = dataset.data.filter(point => point && point.x !== undefined && point.y !== undefined);
            }
        });

        this.requestUpdate();
    }
    
    // Custom min-max decimation for preserving extremes
    applyMinMaxDecimation(data, maxPoints) {
        if (data.length <= maxPoints) {
            return data;
        }

        const result = [];
        const bucketSize = Math.ceil(data.length / maxPoints);

        for (let i = 0; i < data.length; i += bucketSize) {
            const bucket = data.slice(i, Math.min(i + bucketSize, data.length));

            if (bucket.length === 1) {
                result.push(bucket[0]);
            } else if (bucket.length > 1) {
                // Find min and max in bucket
                let min = bucket[0];
                let max = bucket[0];

                for (let j = 1; j < bucket.length; j++) {
                    if (bucket[j].y < min.y) min = bucket[j];
                    if (bucket[j].y > max.y) max = bucket[j];
                }

                // Add both min and max (in chronological order)
                if (min.x < max.x) {
                    result.push(min);
                    if (min !== max) result.push(max);
                } else {
                    result.push(max);
                    if (min !== max) result.push(min);
                }
            }
        }

        return result;
    }

    // High-performance decimation with adaptive bucket sizing
    applyHighPerformanceMinMaxDecimation(data, maxPoints) {
        if (data.length <= maxPoints) {
            return data;
        }

        const result = [];
        const bucketSize = Math.floor(data.length / maxPoints);
        const remainder = data.length % maxPoints;

        let dataIndex = 0;

        for (let bucket = 0; bucket < maxPoints && dataIndex < data.length; bucket++) {
            // Adaptive bucket size - distribute remainder across buckets
            const currentBucketSize = bucketSize + (bucket < remainder ? 1 : 0);
            const bucketEnd = Math.min(dataIndex + currentBucketSize, data.length);

            if (dataIndex >= bucketEnd) break;

            if (currentBucketSize === 1) {
                result.push(data[dataIndex]);
            } else {
                // Fast min-max finding with single pass
                let min = data[dataIndex];
                let max = data[dataIndex];
                let minIndex = dataIndex;
                let maxIndex = dataIndex;

                for (let j = dataIndex + 1; j < bucketEnd; j++) {
                    if (data[j].y < min.y) {
                        min = data[j];
                        minIndex = j;
                    }
                    if (data[j].y > max.y) {
                        max = data[j];
                        maxIndex = j;
                    }
                }

                // Add points in chronological order
                if (minIndex < maxIndex) {
                    result.push(min);
                    if (minIndex !== maxIndex) result.push(max);
                } else if (maxIndex < minIndex) {
                    result.push(max);
                    result.push(min);
                } else {
                    result.push(min); // min and max are the same point
                }
            }

            dataIndex = bucketEnd;
        }

        return result;
    }
    
    requestUpdate() {
        this.needsUpdate = true;
        this.throttledUpdate();
    }

    forceUpdate() {
        // Force immediate update without throttling
        this.needsUpdate = true;
        try {
            const startTime = performance.now();
            this.chart.update('none'); // Use 'none' mode for fastest updates
            const updateTime = performance.now() - startTime;

            // Adaptive performance adjustment
            if (updateTime > 16) { // More than one frame (60fps)
                this.adjustPerformanceSettings(updateTime);
            }

            this.needsUpdate = false;
        } catch (error) {
            console.error('Chart force update error:', error);
            if (this.debug) {
                console.log('Chart datasets:', this.chart.data.datasets);
            }
        }
    }
    
    throttle(func, limit) {
        let lastFunc;
        let lastRan;
        return function() {
            const context = this;
            const args = arguments;
            if (!lastRan) {
                func.apply(context, args);
                lastRan = Date.now();
            } else {
                clearTimeout(lastFunc);
                lastFunc = setTimeout(function() {
                    if ((Date.now() - lastRan) >= limit) {
                        func.apply(context, args);
                        lastRan = Date.now();
                    }
                }, limit - (Date.now() - lastRan));
            }
        }
    }
    
    setTimeWindowSeconds(seconds) {
        this.timeWindowSeconds = seconds;
        this.updateTimeAxisDisplay();
        // Reset time tracking to force updates when time span changes
        this.lastStartTime = null;
        this.lastEndTime = null;
    }
    
    getChart() {
        return this.chart;
    }
    
    // Destroy chart properly
    destroy() {
        if (this.chart) {
            this.chart.destroy();
        }
    }
}

// Export for use in other modules
window.ChartManager = ChartManager;
