/**
 * Color Manager Class
 * Manages color assignment for metrics with collision detection
 */
class ColorManager {
    constructor() {
        this.colors = [
            'rgb(75, 192, 192)',   // <PERSON><PERSON>
            'rgb(255, 99, 132)',   // <PERSON>
            'rgb(54, 162, 235)',   // <PERSON>
            'rgb(255, 159, 64)',   // Orange
            'rgb(153, 102, 255)',  // Purple
            'rgb(255, 205, 86)',   // Yellow
            'rgb(201, 203, 207)',  // Gray
            'rgb(255, 99, 71)',    // Red
            'rgb(50, 205, 50)',    // <PERSON>
            'rgb(30, 144, 255)',   // Dodger Blue
            'rgb(255, 20, 147)',   // Deep Pink
            'rgb(0, 191, 255)',    // Deep Sky Blue
            'rgb(255, 140, 0)',    // Dark Orange
            'rgb(138, 43, 226)',   // Blue Violet
            'rgb(34, 139, 34)',    // Forest Green
            'rgb(220, 20, 60)',    // Crimson
            'rgb(0, 206, 209)',    // Dark Turquoise
            'rgb(255, 69, 0)',     // Red Orange
            'rgb(72, 61, 139)',    // Dark Slate Blue
            'rgb(255, 215, 0)'     // Gold
        ];
        this.metricColorMap = new Map();
    }
    
    getMetricColor(metricName) {
        if (this.metricColorMap.has(metricName)) {
            return this.metricColorMap.get(metricName);
        }

        const usedColors = new Set(this.metricColorMap.values());
        const availableColor = this.colors.find(color => !usedColors.has(color));

        if (availableColor) {
            this.metricColorMap.set(metricName, availableColor);
            return availableColor;
        } else {
            const newColor = this.generateDistinctColor(usedColors);
            this.metricColorMap.set(metricName, newColor);
            return newColor;
        }
    }
    
    generateDistinctColor(usedColors) {
        let attempts = 0;
        const maxAttempts = 50;
        
        while (attempts < maxAttempts) {
            const hue = Math.floor(Math.random() * 360);
            const saturation = 60 + Math.floor(Math.random() * 40); // 60-100%
            const lightness = 40 + Math.floor(Math.random() * 20);  // 40-60%
            
            const color = `hsl(${hue}, ${saturation}%, ${lightness}%)`;
            
            if (!this.isColorTooSimilar(color, usedColors)) {
                return color;
            }
            
            attempts++;
        }
        
        // Fallback: generate a color based on metric name hash
        return this.generateHashBasedColor(usedColors.size);
    }
    
    generateHashBasedColor(index) {
        // Generate color based on index to ensure consistency
        const hue = (index * 137.508) % 360; // Golden angle approximation
        const saturation = 70;
        const lightness = 50;
        return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
    }
    
    isColorTooSimilar(color, usedColors) {
        const [h, s, l] = this.parseHSL(color);
        
        for (const usedColor of usedColors) {
            const usedHSL = this.parseHSL(usedColor);
            if (!usedHSL) continue;
            
            const [uh, us, ul] = usedHSL;
            
            // Check if colors are too similar in hue, saturation, or lightness
            const hueDiff = Math.min(Math.abs(h - uh), 360 - Math.abs(h - uh));
            if (hueDiff < 30 && Math.abs(s - us) < 20 && Math.abs(l - ul) < 15) {
                return true;
            }
        }
        
        return false;
    }
    
    parseHSL(color) {
        // Handle both HSL and RGB formats
        if (color.startsWith('hsl')) {
            const match = color.match(/hsl\((\d+),\s*(\d+)%,\s*(\d+)%\)/);
            if (match) {
                return [parseInt(match[1]), parseInt(match[2]), parseInt(match[3])];
            }
        } else if (color.startsWith('rgb')) {
            // Convert RGB to HSL
            const match = color.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
            if (match) {
                const r = parseInt(match[1]) / 255;
                const g = parseInt(match[2]) / 255;
                const b = parseInt(match[3]) / 255;
                
                const max = Math.max(r, g, b);
                const min = Math.min(r, g, b);
                let h, s, l = (max + min) / 2;
                
                if (max === min) {
                    h = s = 0; // achromatic
                } else {
                    const d = max - min;
                    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
                    switch (max) {
                        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                        case g: h = (b - r) / d + 2; break;
                        case b: h = (r - g) / d + 4; break;
                    }
                    h /= 6;
                }
                
                return [Math.round(h * 360), Math.round(s * 100), Math.round(l * 100)];
            }
        }
        
        return null;
    }
    
    // Get all assigned colors
    getAllColors() {
        return Array.from(this.metricColorMap.entries());
    }
    
    // Reset color assignments
    reset() {
        this.metricColorMap.clear();
    }
    
    // Remove color assignment for a metric
    removeMetric(metricName) {
        this.metricColorMap.delete(metricName);
    }
}

// Export for use in other modules
window.ColorManager = ColorManager;
