"""
Robust MQTT Client with SQLite-based message buffering and automatic retry capabilities.

This module provides a resilient MQTT client that:
- Uses SQLite for persistent message buffering
- Implements automatic retry logic with configurable intervals
- Handles connection state management with indefinite reconnection
- Provides message ordering
- Ensures thread-safe operations
"""

import glob
import queue
import threading
import time
import sqlite3
import logging
import os
from typing import Optional, Dict, Any, Callable
from dataclasses import dataclass
from awscrt import io, mqtt, mqtt_request_response, io
from awsiot import mqtt_connection_builder, iotshadow, ServiceStreamOptions
import concurrent.futures
from threading import Lock

import orjson


@dataclass
class ProcessingConfig:
    """Configuration for message processing behavior."""
    processing_interval: float = 0.5  # seconds
    vacuum_interval: float = 60.0  # 1 minute in seconds


@dataclass
class MQTTConfig:
    """Configuration for MQTT connection."""
    endpoint: str
    client_id: str
    cert_filepath: str
    pri_key_filepath: str
    ca_filepath: str
    clean_session: bool = False
    keep_alive_secs: int = 10
    ping_timeout_ms: int = 3000
    protocol_operation_timeout_ms: int = 5000
    tcp_connect_timeout_ms: int = 5000
    reconnect_min_timeout_secs: int = 1
    reconnect_max_timeout_secs: int = 5


class RobustMQTTClient:
    """
    A robust MQTT client with SQLite-based message buffering and automatic retry capabilities.
    
    Features:
    - Persistent message storage using SQLite
    - Automatic connection retry with exponential backoff
    - Thread-safe operations
    - Non-blocking publish and subscribe operations
    - Graceful shutdown and cleanup
    """

    def __init__(
        self,
        mqtt_config: MQTTConfig,
        processing_config: Optional[ProcessingConfig] = None,
        db_path: str = "mqtt_buffer.db",
        logger: Optional[logging.Logger] = None,
        clear=False
    ):
        """
        Initialize the robust MQTT client.
        
        Args:
            mqtt_config: MQTT connection configuration
            processing_config: Message processing behavior configuration
            db_path: Path to SQLite database file
            logger: Optional logger instance
        """
        self.mqtt_config = mqtt_config
        self.processing_config = processing_config or ProcessingConfig()
        self.db_path = db_path
        self.logger = logger or logging.getLogger(__name__)
        
        # Message rate tracking
        self._deleted_messages = 0
        self._last_rate_calculation = time.time()
        self._messages_per_second = 0.0
        self._rate_lock = Lock()
        
        # Connection state management
        self.is_connected = threading.Event()
        self.is_connected.clear()
        self.shutdown_event = threading.Event()
        
        # Thread management
        self.connection_thread: Optional[threading.Thread] = None
        self.processing_thread: Optional[threading.Thread] = None
        self.subscription_thread: Optional[threading.Thread] = None
        self.vacuum_thread: Optional[threading.Thread] = None
        self.batch_update_thread: Optional[threading.Thread] = None
        self.batch_delete_thread: Optional[threading.Thread] = None
        self.batch_insert_thread: Optional[threading.Thread] = None

        self.threads_started = False
        self.initial_connect = False
        
        # Callback storage
        self._callbacks: Dict[str, Callable[[str, bytes], None]] = {}

        if clear:
            self.logger.warning("Wiping persistence DB! Do not run with clear=true in production!")
            for file_path in glob.glob(db_path+'*'):
                if os.path.exists(file_path):
                    os.remove(file_path)
                    self.logger.info(f"Removed database file: {file_path}")
            
        
        # Database connection (thread-safe with synchronized mode)
        self.db_connection = sqlite3.connect(self.db_path, check_same_thread=False, isolation_level=None)
        self.db_connection.execute("PRAGMA synchronous = NORMAL")
        # self.db_connection.execute("PRAGMA synchronous = OFF") # if we want to go faster, risk involved
        self.db_connection.execute("PRAGMA journal_mode = WAL")
        # self.db_connection.execute("PRAGMA journal_mode = MEMORY") faster, risk involved
        # self.db_connection.execute("PRAGMA journal_mode = OFF") # faster, very risky
        # Enable memory-mapped I/O with a large allocation (256MB)
        self.db_connection.execute('PRAGMA mmap_size=268435456;')
        # self.db_connection.execute('PRAGMA cache_size = -65536;') # on the heap
        self.db_connection.execute('PRAGMA cache_size = 65536;') # fixed memory block size
        
        # MQTT connection
        self.mqtt_connection = None
        self.shadow_client = None

        self._ids_to_delete = set()
        self._ids_to_retry = set()

        self._update_batch_size = 1000
        self._delete_batch_size = 1000
        
        self._update_lock = Lock()
        self._delete_lock = Lock()

        # batch inserts at a 1000 or 1 second, whichever is first
        # quueue size is 250 because of 100 max MQTT per second per connection AWS quota
        self.insert_queue = queue.Queue(maxsize=250)
        self.insert_batch_size = 1000
        self.insert_batch_seconds = 1
        
        # Initialize database
        self._init_database()
        
        # Setup MQTT connection
        self._setup_mqtt_connection()

    def _init_database(self):
        """Initialize SQLite database with required schema."""
        self.db_connection.executescript('''
            CREATE TABLE IF NOT EXISTS message_buffer (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                topic TEXT NOT NULL,
                payload TEXT NOT NULL,
                qos INTEGER NOT NULL DEFAULT 1,
                timestamp REAL NOT NULL,
                is_processing INTEGER NOT NULL DEFAULT 0,
                last_attempt_at REAL
            );
            CREATE INDEX IF NOT EXISTS idx_timestamp ON message_buffer(timestamp);
            CREATE INDEX IF NOT EXISTS idx_processing ON message_buffer(is_processing);

            CREATE TABLE IF NOT EXISTS subscription_buffer (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                topic TEXT NOT NULL,
                qos INTEGER NOT NULL DEFAULT 1,
                callback_name TEXT NOT NULL,
                timestamp REAL NOT NULL,
                is_processing INTEGER NOT NULL DEFAULT 0,
                last_attempt_at REAL
            );
            CREATE INDEX IF NOT EXISTS idx_sub_timestamp ON subscription_buffer(timestamp);
            CREATE INDEX IF NOT EXISTS idx_sub_processing ON subscription_buffer(is_processing);
        ''')
        
        # Reset any messages that were marked as processing
        self.db_connection.execute('UPDATE message_buffer SET is_processing = 0')
        self.db_connection.execute('UPDATE subscription_buffer SET is_processing = 0')

    def _setup_mqtt_connection(self):
        """Setup MQTT connection with callbacks."""
        try:
            self.mqtt_connection = mqtt_connection_builder.mtls_from_path(
                endpoint=self.mqtt_config.endpoint,
                cert_filepath=self.mqtt_config.cert_filepath,
                pri_key_filepath=self.mqtt_config.pri_key_filepath,
                client_bootstrap=io.ClientBootstrap.get_or_create_static_default(),
                ca_filepath=self.mqtt_config.ca_filepath,
                client_id=self.mqtt_config.client_id,
                clean_session=self.mqtt_config.clean_session,
                keep_alive_secs=self.mqtt_config.keep_alive_secs,
                ping_timeout_ms=self.mqtt_config.ping_timeout_ms,
                protocol_operation_timeout_ms=self.mqtt_config.protocol_operation_timeout_ms,
                tcp_connect_timeout_ms=self.mqtt_config.tcp_connect_timeout_ms,
                reconnect_min_timeout_secs=self.mqtt_config.reconnect_min_timeout_secs,
                reconnect_max_timeout_secs=self.mqtt_config.reconnect_max_timeout_secs,
                on_connection_interrupted=self._on_connection_interrupted,
                on_connection_resumed=self._on_connection_resumed,
                on_connection_success=self._on_connection_success,
                on_connection_failure=self._on_connection_failure,
            )
        except Exception as e:
            self.logger.error(f"Failed to setup MQTT connection: {e}")
            raise

    def _setup_shadow_connection(self):
        rr_options = mqtt_request_response.ClientOptions(
            max_request_response_subscriptions = 2,
            max_streaming_subscriptions = 2,
            operation_timeout_in_seconds = 30,
        )
        self.shadow_client = iotshadow.IotShadowClientV2(self.mqtt_connection, rr_options)
        
        self.shadow_client.create_shadow_delta_updated_stream(
            request=iotshadow.ShadowDeltaUpdatedSubscriptionRequest(
                thing_name="DRONE_NAME"
            ),
            options=ServiceStreamOptions(
                incoming_event_listener=self._on_shadow_delta_updated
            )
        )

        self.shadow_client.create_shadow_updated_stream(
            request=iotshadow.ShadowUpdatedSubscriptionRequest(
                thing_name="DRONE_NAME"
            ),
            options=ServiceStreamOptions(
                incoming_event_listener=self._on_shadow_update
            )
        )

        self.shadow_client.get_shadow(
            request=iotshadow.GetShadowRequest(
                client_token="",
                thing_name="DRONE_NAME"
            ),
        ).add_done_callback(self._on_shadow_gotten)


        self.shadow_client.get_shadow(
            request=iotshadow.GetShadowRequest(
                client_token="",
                thing_name="DRONE_NAME"
            ),
        ).add_done_callback(self._on_shadow_gotten)

    
    def _on_shadow_update(self, event: iotshadow.ShadowUpdatedEvent):
        pass
    
    def _on_shadow_delta_updated(self, event: iotshadow.ShadowDeltaUpdatedEvent):
        pass
    
    def _on_shadow_gotten(self, ftr: concurrent.futures.Future):
        pass


    def _on_connection_success(self, connection, callback_data):
        """Callback for successful connection."""
        self.logger.info("MQTT connection established successfully")
        self.is_connected.set()

    def _on_connection_failure(self, connection, callback_data):
        """Callback for connection failure."""
        self.logger.warning(f"MQTT connection failed: {callback_data}")
        self.is_connected.clear()

    def _on_connection_interrupted(self, connection, error, **kwargs):
        """Callback for connection interruption."""
        self.logger.warning(f"MQTT connection interrupted: {error}")
        self.is_connected.clear()

    def _on_connection_resumed(self, connection, return_code, session_present, **kwargs):
        """Callback for connection resumption."""
        self.logger.info(f"MQTT connection resumed: return_code={return_code}, session_present={session_present}")
        self.is_connected.set()

    def _delete_batch(self):
        
        with self._delete_lock:
            # Get up to BATCH_SIZE messages to delete
            ids_to_delete = list(self._ids_to_delete)[:self._delete_batch_size]
            if not ids_to_delete:
                return False
                
            # Remove the processed IDs from the set
            self._ids_to_delete = self._ids_to_delete - set(ids_to_delete)
            
        # Create placeholders for SQL query
        placeholders = ','.join('?' * len(ids_to_delete))
            
        try:
            # Execute delete query
            self.db_connection.execute(f'''
                DELETE FROM message_buffer 
                WHERE id IN ({placeholders})
            ''', ids_to_delete)
            
            # Update message rate tracking
            with self._rate_lock:
                self._deleted_messages += len(ids_to_delete)
                current_time = time.time()
                time_diff = current_time - self._last_rate_calculation
                
                if time_diff >= 5.0:  # Calculate rate every second
                    self._messages_per_second = self._deleted_messages / time_diff
                    self._deleted_messages = 0
                    self._last_rate_calculation = current_time
            
        except Exception as e:
            self.logger.error(f"Failed to delete messages from database: {e}")
            # Add failed IDs back to the set
            with self._delete_lock:
                self._ids_to_delete.update(ids_to_delete)

            return False

        return True

    def _insert_processor(self):
        while not self.shutdown_event.isSet():
            try:
                batch_start_time = time.time()
                records = []
                while(time.time() - batch_start_time < self.insert_batch_seconds and 
                       len(records) < self.insert_batch_size and 
                       not self.insert_queue.empty() ):
                    try:
                        record = self.insert_queue.get(timeout=1)
                        records.append(record)
                    except queue.Empty:
                        time.sleep(0.01)

                if records:
                    cursor = self.db_connection.cursor()
                    self.db_connection.execute("BEGIN TRANSACTION;")
                    cursor.executemany('''
                        INSERT INTO message_buffer
                        (topic, payload, qos, timestamp)
                        VALUES (?, ?, ?, ?)
                    ''', records)

                    self.db_connection.execute("COMMIT;")
                    
                    self.logger.info(f"Inserted {len(records)} MQTT messages in batch")
                
                # If we processed less than 1000 records and haven't used our full time,
                # sleep for the remaining time
                if len(records) < self.insert_batch_size:
                    elapsed = time.time() - batch_start_time
                    if elapsed < self.insert_batch_seconds:
                        time.sleep(self.insert_batch_seconds - elapsed)
                else:
                    continue

            except Exception as e:
                self.logger.error(f"Error in INSERT batch processor: {e}")
                time.sleep(1)  # Sleep longer on error


    def _update_batch(self):
        
        with self._update_lock:
            # Get up to BATCH_SIZE messages to delete
            ids_to_update = list(self._ids_to_retry)[:self._update_batch_size]
            if not ids_to_update:
                return False
                
            # Remove the processed IDs from the set
            self._ids_to_retry = self._ids_to_retry - set(ids_to_update)
            
        # Create placeholders for SQL query
        placeholders = ','.join('?' * len(ids_to_update))
            
        try:
            # Execute delete query
            self.db_connection.execute(f'''
                UPDATE message_buffer
                SET is_processing = 0
                WHERE id IN ({placeholders})
            ''', ids_to_update)
            
            # self.logger.debug(f"Updated {len(ids_to_update)} messages from database")
            
        except Exception as e:
            self.logger.error(f"Failed to update messages from database: {e}")
            # Add failed IDs back to the set
            with self._delete_lock:
                self._ids_to_retry.update(ids_to_update)

            return False

        return True
    
    def _delete_message_processor(self):
        """Background worker to delete sent messages."""
        while not self.shutdown_event.is_set():

            did_process = self._delete_batch()
            
            # If messages were processed, continue to the next cycle
            if did_process:
                continue

            # Wait before next processing cycle only if no messages are pending
            self.shutdown_event.wait(timeout=self.processing_config.processing_interval)


    def _update_message_processor(self):
        """Background worker to update sent messages."""
        while not self.shutdown_event.is_set():

            did_process = self._update_batch()

            # If messages were processed, continue to the next cycle
            if did_process:
                continue

            # Wait before next processing cycle only if no messages are pending
            self.shutdown_event.wait(timeout=self.processing_config.processing_interval)


    def _on_publish_complete(self, future, msg_id: int):
        """Callback for when a message publish completes."""
        try:
            future.result()  # This will raise an exception if publish failed
            # Delete the message on successful publish
            try:
                with self._delete_lock:
                    self._ids_to_delete.add(msg_id)

                # self.logger.debug(f"Successfully published and removed message {msg_id}")
            except Exception as e:
                self.logger.error(f"Failed to remove successful message from database: {e}")
        except Exception as e:
            # Mark message as not being processed so it will be retried
            try:
                with self._update_lock:
                    self._ids_to_retry.add(msg_id)

                self.logger.debug(f"Failed to publish message {msg_id}, will retry later: {e}")
            except Exception as db_error:
                self.logger.error(f"Failed to update message status in database: {db_error}")

    def start(self):
        """Start the MQTT client and background threads."""
        if self.threads_started:
            self.logger.warning("Client already started")
            return
            
        self.logger.info("Starting robust MQTT client...")

        self.batch_insert_thread = threading.Thread(target=self._insert_processor, daemon=True)
        self.batch_insert_thread.start()
        
        # Start connection management thread
        self.connection_thread = threading.Thread(target=self._connection_manager, daemon=True)
        self.connection_thread.start()
        
        # Start message processing thread
        self.processing_thread = threading.Thread(target=self._message_processor, daemon=True)
        self.processing_thread.start()
        
        # Start subscription processing thread
        self.subscription_thread = threading.Thread(target=self._subscription_processor, daemon=True)
        self.subscription_thread.start()
        
        # Start vacuum thread
        self.vacuum_thread = threading.Thread(target=self._vacuum_worker, daemon=True)
        self.vacuum_thread.start()

        self.batch_delete_thread = threading.Thread(target=self._delete_message_processor, daemon=True)
        self.batch_delete_thread.start()

        self.batch_update_thread = threading.Thread(target=self._update_message_processor, daemon=True)
        self.batch_update_thread.start()
        
        self.threads_started = True
        self.logger.info("Robust MQTT client started successfully")

    def stop(self):
        """Stop the MQTT client and cleanup resources."""
        self.logger.info("Stopping robust MQTT client...")
        
        # Signal shutdown
        self.shutdown_event.set()
        
        # Disconnect MQTT
        if self.mqtt_connection and self.is_connected.is_set():
            try:
                disconnect_future = self.mqtt_connection.disconnect()
                disconnect_future.result(timeout=5)
            except Exception as e:
                self.logger.error(f"Error during MQTT disconnect: {e}")
        
        # Wait for threads to finish

        if self.batch_insert_thread and self.batch_insert_thread.is_alive():
            self.batch_insert_thread.join(timeout=2)

        if self.connection_thread and self.connection_thread.is_alive():
            self.connection_thread.join(timeout=2)
        if self.processing_thread and self.processing_thread.is_alive():
            self.processing_thread.join(timeout=2)
        if self.subscription_thread and self.subscription_thread.is_alive():
            self.subscription_thread.join(timeout=2)
        if self.vacuum_thread and self.vacuum_thread.is_alive():
            self.vacuum_thread.join(timeout=2)

        if self.batch_update_thread and self.batch_update_thread.is_alive():
            self.batch_update_thread.join(timeout=2)

        if self.batch_delete_thread and self.batch_delete_thread.is_alive():
            self.batch_delete_thread.join(timeout=2)
        
        # Clean up callbacks
        self._callbacks.clear()
        
        self.threads_started = False
        self.logger.info("Robust MQTT client stopped")

    def _connection_manager(self):
        """Manage MQTT connection with indefinite retry."""
        while not self.shutdown_event.is_set():
            if not self.is_connected.is_set():
                try:
                    if not self.initial_connect:
                        self.logger.info("Attempting to connect to MQTT broker...")
                        connect_future = self.mqtt_connection.connect()
                        connect_future.result(timeout=5)
                        self.initial_connect = True
                except Exception as e:
                    self.logger.warning(f"MQTT connection attempt failed: {e}")
                    # Wait before retrying
                    self.shutdown_event.wait(timeout=5)
            else:
                # Wait while connected
                self.shutdown_event.wait(timeout=1)

    def _message_processor(self):
        """Background worker to process pending messages."""
        while not self.shutdown_event.is_set():

            if self.is_connected.is_set():
                did_process = self._process_pending_messages()
                
                # If messages were processed, continue to the next cycle
                if did_process:
                    continue

            # Wait before next processing cycle only if no messages are pending
            self.shutdown_event.wait(timeout=self.processing_config.processing_interval)
            

    def _process_pending_messages(self):
        """Process pending messages from the database."""
        try:
            cursor = self.db_connection.cursor()
            
            # Get pending messages that are not being processed, ordered by timestamp
            cursor.execute('''
                SELECT id, topic, payload, qos, timestamp
                FROM message_buffer
                WHERE is_processing = 0
                ORDER BY timestamp ASC
                LIMIT 100
            ''')

            messages = cursor.fetchall()

            # Mark message as being processed
            if messages:
                current_time = time.time()
                # Update all fetched messages to is_processing = 1 in a single operation
                message_ids = [msg[0] for msg in messages]
                placeholders = ','.join('?' * len(message_ids))
                cursor.execute(f'''
                    UPDATE message_buffer
                    SET is_processing = 1, last_attempt_at = ?
                    WHERE id IN ({placeholders})
                ''', [current_time] + message_ids)
            else:
                return False
            

            for msg_id, topic, payload, qos, timestamp in messages:
                if self.shutdown_event.is_set() or not self.is_connected.is_set():
                    break
                
                # Attempt to publish the message
                self._attempt_publish(topic, payload, qos, msg_id)

            return True

        except Exception as e:
            self.logger.error(f"Error processing pending messages: {e}")

    def _attempt_publish(self, topic: str, payload: str, qos: int, msg_id: int) -> bool:
        """
        Attempt to publish a message via MQTT.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not self.is_connected.is_set():
                return False

            mqtt_qos = mqtt.QoS.AT_MOST_ONCE if qos == 0 else mqtt.QoS.AT_LEAST_ONCE

            publish_future, packet_id = self.mqtt_connection.publish(
                topic=topic,
                payload=payload,
                qos=mqtt_qos
            )
            publish_future.add_done_callback(lambda future: self._on_publish_complete(future, msg_id))
            return True

        except Exception as e:
            self.logger.debug(f"Failed to publish message {msg_id}: {e}")
            return False

    def publish(
        self,
        topic: str,
        payload: dict,
        qos: int = 1,
    ) -> int:
        """
        Publish a message with automatic buffering and retry.
        This is a non-blocking operation that only writes to the database.
        The actual publishing will be handled by the background processing thread.

        Args:
            topic: MQTT topic to publish to
            payload: Message payload as string
            qos: Quality of Service level (0 or 1)

        Returns:
            int: Message ID of the inserted message
        """
        current_time = time.time()

        str_payload = orjson.dumps(payload).decode()

        try:
            self.insert_queue.put(
                (topic, str_payload, qos, current_time)
            )
        except Exception as e:
            self.logger.error(f"Failed to queue message for DB: {e}")
            raise

        return 1


    def subscribe(
        self,
        topic: str,
        callback: Callable[[str, bytes], None],
        qos: int = 1
    ) -> int:
        """
        Subscribe to an MQTT topic with automatic buffering and retry.
        This is a non-blocking operation that only writes to the database.
        The actual subscription will be handled by the background processing thread.

        Args:
            topic: MQTT topic to subscribe to
            callback: Callback function to handle received messages
            qos: Quality of Service level (0 or 1)

        Returns:
            int: Subscription ID of the inserted subscription request
        """
        current_time = time.time()

        # Store callback in the callbacks dictionary
        callback_name = f"callback_{current_time}_{id(callback)}"
        self._callbacks[callback_name] = callback

        # Store subscription request in database
        try:
            cursor = self.db_connection.cursor()
            cursor.execute('''
                INSERT INTO subscription_buffer
                (topic, qos, callback_name, timestamp)
                VALUES (?, ?, ?, ?)
            ''', (topic, qos, callback_name, current_time))
            
            # Get the last inserted row id
            cursor.execute('SELECT last_insert_rowid()')
            subscription_id = cursor.fetchone()[0]

        except Exception as e:
            # Clean up callback if database operation fails
            self._callbacks.pop(callback_name, None)
            self.logger.error(f"Failed to store subscription request in database: {e}")
            raise

        return subscription_id

    def _subscription_processor(self):
        """Background worker to process pending subscriptions."""
        while not self.shutdown_event.is_set():
            if self.is_connected.is_set():
                did_process = self._process_pending_subscriptions()
                
                # If subscriptions were processed, continue to the next cycle
                if did_process:
                    continue

            # Wait before next processing cycle only if no subscriptions are pending
            self.shutdown_event.wait(timeout=self.processing_config.processing_interval)

    def _process_pending_subscriptions(self):
        """Process pending subscriptions from the database."""
        try:
            cursor = self.db_connection.cursor()
            
            # Get pending subscriptions that are not being processed, ordered by timestamp
            cursor.execute('''
                SELECT id, topic, qos, callback_name
                FROM subscription_buffer
                WHERE is_processing = 0
                ORDER BY timestamp ASC
                LIMIT 100
            ''')

            subscriptions = cursor.fetchall()

            current_time = time.time()

            # Mark subscriptions as being processed
            if subscriptions:
                # Update all fetched subscriptions to is_processing = 1 in a single operation
                subscription_ids = [sub[0] for sub in subscriptions]
                placeholders = ','.join('?' * len(subscription_ids))
                cursor.execute(f'''
                    UPDATE subscription_buffer
                    SET is_processing = 1, last_attempt_at = ?
                    WHERE id IN ({placeholders})
                ''', [current_time] + subscription_ids)
            else:
                return False

            for sub_id, topic, qos, callback_name in subscriptions:
                if self.shutdown_event.is_set() or not self.is_connected.is_set():
                    break
                
                # Attempt to subscribe
                if self._attempt_subscribe(topic, qos, callback_name, sub_id):
                    # If successful, remove the subscription request
                    cursor.execute('DELETE FROM subscription_buffer WHERE id = ?', (sub_id,))
                else:
                    # If failed, mark for retry
                    cursor.execute('''
                        UPDATE subscription_buffer
                        SET is_processing = 0
                        WHERE id = ?
                    ''', (sub_id,))

            return True

        except Exception as e:
            self.logger.error(f"Error processing pending subscriptions: {e}")
            return False

    def _attempt_subscribe(self, topic: str, qos: int, callback_name: str, sub_id: int) -> bool:
        """
        Attempt to subscribe to a topic via MQTT.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not self.is_connected.is_set():
                return False

            mqtt_qos = mqtt.QoS.AT_MOST_ONCE if qos == 0 else mqtt.QoS.AT_LEAST_ONCE
            callback = self._callbacks.get(callback_name)
            
            if callback is None:
                self.logger.error(f"Callback {callback_name} not found for subscription {sub_id}")
                return False

            subscribe_future, packet_id = self.mqtt_connection.subscribe(
                topic=topic,
                qos=mqtt_qos,
                callback=callback
            )

            subscribe_future.result(timeout=5)
            self.logger.info(f"Successfully subscribed to topic: {topic}")
            
            # Clean up callback after successful subscription
            self._callbacks.pop(callback_name, None)
            return True

        except Exception as e:
            self.logger.debug(f"Failed to subscribe to topic {topic}: {e}")
            return False

    def unsubscribe(self, topic: str) -> bool:
        """
        Unsubscribe from an MQTT topic.

        Args:
            topic: MQTT topic to unsubscribe from

        Returns:
            bool: True if unsubscription successful, False otherwise
        """
        try:
            if not self.is_connected.is_set():
                self.logger.warning(f"Cannot unsubscribe from {topic}: not connected")
                return False

            unsubscribe_future, packet_id = self.mqtt_connection.unsubscribe(topic=topic)
            unsubscribe_future.result(timeout=5)
            self.logger.info(f"Successfully unsubscribed from topic: {topic}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to unsubscribe from topic {topic}: {e}")
            return False

    def get_pending_message_count(self) -> int:
        """
        Get the number of pending messages in the buffer.

        Returns:
            int: Number of pending messages
        """
        try:
            cursor = self.db_connection.cursor()
            cursor.execute('SELECT COUNT(id) FROM message_buffer')
            count = cursor.fetchone()[0]
            return count
        except Exception as e:
            self.logger.error(f"Failed to get pending message count: {e}")
            return 0

    def get_connection_status(self) -> Dict[str, Any]:
        """
        Get current connection status and statistics.

        Returns:
            dict: Connection status information
        """
        return {
            'connected': self.is_connected.is_set(),
            'pending_messages': self.get_pending_message_count(),
            'threads_started': self.threads_started,
            'client_id': self.mqtt_config.client_id,
            'endpoint': self.mqtt_config.endpoint,
            'rate': self.get_message_rate()
        }

    def __enter__(self):
        """Context manager entry."""
        self.start()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.stop()

    def __del__(self):
        """Destructor to ensure cleanup."""
        if hasattr(self, 'threads_started') and self.threads_started:
            self.stop()
        
        # Close database connection
        if hasattr(self, 'db_connection'):
            try:
                self.db_connection.close()
            except Exception as e:
                if hasattr(self, 'logger'):
                    self.logger.error(f"Error closing database connection: {e}")

    def _vacuum_worker(self):
        """Background worker to periodically vacuum the database when needed."""
        while not self.shutdown_event.is_set():
            try:
                # Check if vacuum is needed by comparing free pages to total pages
                cursor = self.db_connection.cursor()
                cursor.execute("PRAGMA page_count")
                total_pages = cursor.fetchone()[0]
                cursor.execute("PRAGMA freelist_count")
                free_pages = cursor.fetchone()[0]
                
                # If more than 20% of pages are free, run vacuum
                if total_pages > 0 and (free_pages / total_pages) > 0.2:
                    self.logger.info(f"Starting database vacuum operation (free pages: {free_pages}/{total_pages})...")
                    self.db_connection.execute("VACUUM")
                    self.logger.info("Database vacuum operation completed successfully")
                else:
                    self.logger.debug(f"Skipping vacuum - free pages ratio ({free_pages}/{total_pages}) below threshold")
            except Exception as e:
                self.logger.error(f"Error during database vacuum operation: {e}")
            
            # Wait for the configured interval before next check
            self.shutdown_event.wait(timeout=self.processing_config.vacuum_interval)

    def get_message_rate(self) -> float:
        """
        Get the current message processing rate in messages per second.

        Returns:
            float: Current message processing rate
        """
        with self._rate_lock:
            return self._messages_per_second
