"""
Modified version of main.py using the RobustMQTTClient.

This demonstrates how to integrate the robust MQTT client into your existing ROS2 node.
"""
import orjson
import time
import threading
from dummy import Dummy<PERSON><PERSON><PERSON><PERSON>lient
from socket_listener import SocketListener
from message_batcher import MessageBatcher
import logging

METRIC_TOPIC = 'some/foobar/topic'
TENANT = 'DUMMY'

class LocalGraph:

    def __init__(self):
        
        self.logger = logging.getLogger()
        self.logger.setLevel(logging.INFO)

        self.running = True

        self.batcher = MessageBatcher(
            max_batch_size=1000,
            max_interval=0.1  # 100ms for high-frequency data
        )
        self.batcher.set_callback(self.transmit_metrics)

        self.mqtt_client = DummyMQTTClient(
            clear=True,
            archive=True,
            db_path='local_graph.db',
            run_server=True,
            logger=self.logger,
            no_ros=True
        )
        
        self.mqtt_client.start()
        self.logger.info("Robust MQTT client started")

        self.socket_listener = SocketListener(self.logger)
        self.socket_listener.configure()



    def message_sender_thread(self):
        """Thread function that continuously sends messages."""
        while self.running:
            try:
                data = self.socket_listener.get_message_from_socket_blocking()

                if not data:
                    continue

                ts = time.time()

                data = orjson.loads(data)

                for k, v in data.items():
                    self.batcher.add_message(
                        {
                            "name": str(k),
                            "value": float(v),
                            "timestamp": ts,
                            'group': "DUMMY"
                        }
                    )
            except Exception as ex:
                self.logger.error(f"Could not get data from socket! : {ex}")

    def transmit_metrics(self, metrics):
        try:
            self.logger.info(f"Queueing batch of {len(metrics)} records")
            aws_message = {
                "type": "DB_MULTIRECORD",
                "tenant": TENANT,
                "drone_id": 1,
                # "timestamp": ts,
                'values': metrics
            }

            # optionally add to server
            # optionally add to metrics

            message_id = self.mqtt_client.publish(METRIC_TOPIC, aws_message)

        except Exception as ex:
            self.logger.error(f"Could not send metrics: {ex}")


    def run(self):
        """Start the message sender and status logger threads."""
        # Create and start threads
        sender_thread = threading.Thread(target=self.message_sender_thread, name="MessageGenerator")
        
        sender_thread.start()
        
        try:
            # Keep main thread alive
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            self.running = False
            sender_thread.join()
            self.mqtt_client.stop()

        sender_thread.join()
        self.mqtt_client.stop()


    def __del__(self):
        self.mqtt_client.stop()
        # sys.exit(0)

def main(args=None):
    
    http_server_node = LocalGraph()
    http_server_node.run()

if __name__ == '__main__':
    main()
