"""
Dummy MQTT Client that processes and stores MQTT messages in SQLite.

This module provides a simple MQTT client that:
- Parses multirecord and multivalue messages
- Stores extracted data in SQLite
- Provides basic message processing capabilities
- Serves data via web endpoints
"""

import sqlite3
import logging
import time
from typing import Optional, Dict, Any, List, Tuple
from queue import Queue
import os
from server_component import ServerComponent
from metric_archiver import MetricArchiver
import glob
import threading

class DummyMQTTClient:
    """
    A dummy MQTT client that processes and stores messages in SQLite.
    
    Features:
    - Parses multirecord and multivalue messages
    - Stores data in SQLite with proper schema
    - Provides basic message processing capabilities
    - Serves data via web endpoints
    """

    def __init__(
        self,
        db_path: str = "dummy_mqtt.db",
        logger: Optional[logging.Logger] = None,
        clear: bool = False,
        web_port: int = 8000,
        archive: bool = False,
        run_server: bool = False,
        no_ros=False
    ):
        """
        Initialize the dummy MQTT client.
        
        Args:
            db_path: Path to SQLite database file
            logger: Optional logger instance
            clear: Whether to clear existing database
            web_port: Port for FastAPI web server
        """
        self.db_path = db_path
        self.logger = logger or logging.getLogger(__name__)
        self.web_port = web_port
        
        # Create data queue for SSE
        self.data_queue = Queue(maxsize=50)

        # Create batch queue for database inserts
        self.batch_queue = Queue()
        self.batch_thread = None
        self.batch_running = False
        self.batch_size = 50000
        self.batch_seconds = 1

        self.do_archive = archive
        self.do_server = run_server
        self.archiver = None
        
        if clear:
            self.logger.warning("Wiping persistence DB! Do not run with clear=true in production!")
            for file_path in glob.glob(db_path+'*'):
                if os.path.exists(file_path):
                    os.remove(file_path)
                    self.logger.info(f"Removed database file: {file_path}")

        if archive:
            self.archive_dir = "debug_archives"
            self.archiver = MetricArchiver(archive_dir = self.archive_dir)
        
        # Database connection (thread-safe with synchronized mode)
        self.db_connection = sqlite3.connect(self.db_path, check_same_thread=False, isolation_level=None)
        self.db_connection.execute("PRAGMA synchronous = NORMAL")
        self.db_connection.execute("PRAGMA journal_mode = WAL")
        self.db_connection.execute('PRAGMA mmap_size=268435456;')  # 256MB
        self.db_connection.execute('PRAGMA cache_size = 65536;')  # fixed memory block size

        # Initialize server component
        if self.do_server:
            self.server = ServerComponent(
                data_queue=self.data_queue,
                db_connection=self.db_connection,
                logger=self.logger,
                web_port=self.web_port,
                no_ros=no_ros
            )

    def _batch_processor(self):
        """Process batches of records for database insertion."""
        while self.batch_running:
            try:
                # Get records from the queue, up to 1000 at a time
                records = []
                start_time = time.time()
                
                # Process for at most 1 second or until we have 1000 records
                while (time.time() - start_time < self.batch_seconds and 
                       len(records) < self.batch_size and 
                       not self.batch_queue.empty()):
                    try:
                        batch = self.batch_queue.get_nowait()
                        records.extend(batch)
                    except Exception as ex:
                        time.sleep(0.01)
                        pass
                
                if records:
                    # Insert records into database
                    cursor = self.db_connection.cursor()
                    self.db_connection.execute("BEGIN TRANSACTION;")
                    cursor.executemany('''
                        INSERT INTO message_data 
                        (name, value, timestamp)
                        VALUES (?, ?, ?)
                    ''', records)

                    self.db_connection.execute("COMMIT;")
                    
                    self.logger.info(f"Inserted {len(records)} records in batch")

                
                # If we processed less than 1000 records and haven't used our full time,
                # sleep for the remaining time
                if len(records) < self.batch_size:
                    elapsed = time.time() - start_time
                    if elapsed < self.batch_seconds:
                        time.sleep(self.batch_seconds - elapsed)
                else:
                    continue
                
            except Exception as e:
                self.logger.error(f"Error in batch processor: {e}")
                time.sleep(1)  # Sleep longer on error

    def start(self):
        """Start the dummy MQTT client and web server."""
        # Initialize database
        self._init_database()
        
        # Start batch processor thread
        self.batch_running = True
        self.batch_thread = threading.Thread(target=self._batch_processor, daemon=True, name="BatchDummyInsert")
        self.batch_thread.start()
        
        # Start server
        if self.do_server:
            self.server.start()
        self.logger.info("Dummy MQTT client started")

    def stop(self):
        """Stop the dummy MQTT client and cleanup resources."""
        self.logger.info("Stopping dummy MQTT client...")

        # Stop server
        if self.do_server:
            self.server.stop()
        
        # Stop batch processor
        self.batch_running = False
        if self.batch_thread:
            self.batch_thread.join(timeout=5.0)
        if self.do_archive:
            self.archiver.stop()
        
        # Close database connection
        if hasattr(self, 'db_connection'):
            try:
                self.db_connection.close()
                self.logger.info("Database connection closed")
            except Exception as e:
                self.logger.error(f"Error closing database connection: {e}")
        
        self.logger.info("Dummy MQTT client stopped")

    def _init_database(self):
        """Initialize SQLite database with required schema."""
        self.db_connection.executescript('''
            CREATE TABLE IF NOT EXISTS message_data (
                name TEXT NOT NULL,
                value REAL NOT NULL,
                timestamp REAL NOT NULL
            );
            CREATE INDEX IF NOT EXISTS idx_timestamp ON message_data(timestamp);
            CREATE INDEX IF NOT EXISTS idx_name ON message_data(name);
        ''')

    def _do_db_insert(self, records: List[Tuple]) -> bool:
        """
        Queue records for batch insertion into the database and immediately send to SSE.
        
        Args:
            records: List of tuples (drone_id, name, value, timestamp, message_type, created_at)
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if records:
                # Immediately send to SSE queue
                # Queue for batch database insert
                self.batch_queue.put(records)
                for record in records:
                    metric_dict = {
                        'name': record[0],
                        'value': record[1],
                        'timestamp': record[2],
                    }

                    try:
                        self.data_queue.put_nowait(metric_dict)
                    except Exception as ex:
                        pass

                return True
            return False
        except Exception as e:
            self.logger.error(f"Failed to queue records: {e}")
            return False

    def _process_multirecord(self, message: Dict[str, Any]) -> bool:
        """Process a multirecord message."""
        try:
            drone_id = message.get("drone_id")
            if drone_id is None:
                self.logger.error("Missing drone_id in multirecord message")
                return False

            current_time = time.time()
            records = []
            
            for value_data in message.get("values", []):
                records.append((
                    value_data.get("name", "unknown"),
                    value_data.get("value", 0.0),
                    value_data.get("timestamp", current_time),
                ))
            
            return self._do_db_insert(records)
            
        except Exception as e:
            self.logger.error(f"Failed to process multirecord message: {e}")
            return False

    def _process_multivalue(self, message: Dict[str, Any]) -> bool:
        """Process a multivalue message."""
        try:
            drone_id = message.get("drone_id")
            if drone_id is None:
                self.logger.error("Missing drone_id in multivalue message")
                return False

            timestamp = message.get("timestamp", time.time())
            current_time = time.time()
            records = []
            
            for name, value in message.get("values", {}).items():
                records.append((
                    name,
                    float(value),
                    timestamp,
                ))
            
            return self._do_db_insert(records)
            
        except Exception as e:
            self.logger.error(f"Failed to process multivalue message: {e}")
            return False

    def publish(self, topic: str, payload: dict) -> bool:
        """
        Process an MQTT message and store its data in SQLite.
        
        Args:
            topic: MQTT topic
            payload: Message payload as string
            
        Returns:
            bool: True if processing was successful, False otherwise
        """
        try:
            # Parse the message
            message = payload
            message_type = message.get("type", "UNKNOWN")

            if self.do_archive:
                self.archiver.archive_metric(message)
            
            if message_type == "DB_MULTIRECORD":
                return self._process_multirecord(message)
            elif message_type == "DB_MULTIVALUE":
                return self._process_multivalue(message)
            else:
                self.logger.warning(f"Unknown message type: {message_type}")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to process message: {e}")
            return False

    def get_message_count(self) -> int:
        """
        Get the total number of messages in the database.
        
        Returns:
            int: Number of messages
        """
        try:
            cursor = self.db_connection.cursor()
            cursor.execute('SELECT COUNT(id) FROM message_data')
            return cursor.fetchone()[0]
        except Exception as e:
            self.logger.error(f"Failed to get message count: {e}")
            return 0

    def __del__(self):
        """Destructor to ensure cleanup."""
        if hasattr(self, 'db_connection'):
            try:
                self.db_connection.close()
            except Exception as e:
                if hasattr(self, 'logger'):
                    self.logger.error(f"Error closing database connection: {e}") 