
import rclpy
from rclpy.node import Node
from vision_msgs.msg import Detection2DArray

from iot import <PERSON>ustMQ<PERSON><PERSON><PERSON>, MQTTConfig
from connector import Connector
from aqita_interfaces.srv import MissionStartAck

# Configuration constants
# ENDPOINT = "a3i72wo3tqo4n3-ats.iot.eu-central-1.amazonaws.com"
CLIENT_ID = "drone-1"
TENANT = "uvionix"
# PATH_TO_CERT = "drones/drone-1/certificate.pem"
# PATH_TO_KEY = "drones/drone-1/private.key"
# PATH_TO_ROOT = "root-CA.crt"
# METRIC_TOPIC = f"drone/{TENANT}/{CLIENT_ID}/metrics"
COMMAND_TOPIC = f"drone/{TENANT}/{CLIENT_ID}/commands"
# BARCODE_TOPIC = f"drone/{TENANT}/{CLIENT_ID}/barcode"


class CloudConnector(Node):
    """
    ROS2 node with robust MQTT client integration.
    
    This replaces the original PubSub2 class with a more robust implementation
    that uses SQLite-based message buffering and automatic retry capabilities.
    """

    def mission_ack_service_handler(self, request: MissionStartAck.Request, response: MissionStartAck.Response):
        pass

    def __init__(self):
        super().__init__('cloud_connector')

    
        self.declare_parameter('enable_server', False)
        self.declare_parameter('server_port', 8000)

        self.declare_parameter('enable_archives', False)
        self.declare_parameter('archive_duration', 600)

        self.declare_parameter('mqtt_root_ca', '/workspace/root-CA.crt')
        self.declare_parameter('tenant', 'DUMMY')
        self.declare_parameter('mqtt_endpoint', 'DUMMY')
        self.declare_parameter('mqtt_client_id', 'DUMMY')
        self.declare_parameter('mqtt_client_certificate', '/workspace/certificate.pem.crt')
        self.declare_parameter('mqtt_client_key', '/workspace/private.pem.key')
        self.declare_parameter('mqtt_batch_size', 500)
        self.declare_parameter('mqtt_batch_time', 1)

        # example
        self.TENANT = self.get_parameter('tenant').value
        self.CLIENT_ID = self.get_parameter('mqtt_client_id').value
        self.COMMAND_TOPIC = f"drone/{self.TENANT}/{self.CLIENT_ID}/commands"
        
        self.logger = self.get_logger()

        mqtt_config = MQTTConfig(
            endpoint=str(self.get_parameter('mqtt_endpoint').value),
            client_id=str(self.get_parameter('mqtt_client_id').value),
            cert_filepath=str(self.get_parameter('mqtt_client_certificate').value),
            pri_key_filepath=str(self.get_parameter('mqtt_client_key').value),
            ca_filepath=str(self.get_parameter('mqtt_root_ca').value),
            clean_session=False,
            keep_alive_secs=10,
            ping_timeout_ms=3000,
            protocol_operation_timeout_ms=5000,
            tcp_connect_timeout_ms=3000,
            reconnect_min_timeout_secs=1,
            reconnect_max_timeout_secs=5
        )
        
        self.connector = Connector(logger=self.logger, config=mqtt_config)
        
        self.connector.start()
        self.logger.info("Cloud connector node started ")
        
        # Subscribe to MQTT command topic
        # self.connector.mqtt_client.subscribe(COMMAND_TOPIC, self.connector.mqtt_listener_callback)
        
        # Create ROS2 subscriber
        # self.subscription = self.create_subscription(
        #     msg_type=Detection2DArray,
        #     topic='infer/yolov8/output/detections',
        #     callback=self.connector.ros_listener_callback,
        #     qos_profile=10
        # )
        
        # self.subscription  # prevent unused variable warning
        # self.logger.info('Robust subscriber node started and listening to /infer/yolov8/output/detections')
        
        # Create timer to periodically log status
        # self.status_timer = self.create_timer(5.0, self.log_status)



    def log_status(self):
        """Periodically log connection and buffer status."""
        status = self.connector.mqtt_client.get_connection_status()
        self.logger.info(f"MQTT: Connected={status['connected']}, "
                        f"Pending={status['pending_messages']}, "
                        f"Total={self.connector.msgs_sent}, MPS: {status['rate']}")

    def destroy_node(self):
        """Clean shutdown of the node."""
        self.logger.info("Shutting down robust MQTT client...")
        self.connector.stop()
        super().destroy_node()


def main(args=None):
    """Main function."""
    # Initialize ROS2
    rclpy.init(args=args)
    
    # Create node
    node = CloudConnector()
    
    try:
        # Spin the node
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        # Clean shutdown
        node.destroy_node()
        if rclpy.ok():
            rclpy.shutdown()


if __name__ == '__main__':
    main()
