{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Local Graph",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/aqita_cloud/local_graph_noros.py", // IMPORTANT: Change to your de"debugpy" node's path
            "console": "integratedTerminal",
            "justMyCode": false, // Set to false to step into ROS 2 libraries
            "envFile": "${workspaceFolder}/.env",
            "preLaunchTask": "SOURCE",
            "cwd": "${workspaceFolder}", // Set current working directory to workspace root,
            "python": "/usr/bin/python"
        },
        {
            "name": "Synthetic",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/aqita_cloud/synthetic.py", // IMPORTANT: Change to your de"debugpy" node's path
            "console": "integratedTerminal",
            "justMyCode": false, // Set to false to step into ROS 2 libraries
            "envFile": "${workspaceFolder}/.env",
            "preLaunchTask": "SOURCE",
            "cwd": "${workspaceFolder}", // Set current working directory to workspace root,
            "python": "/usr/bin/python"
        },
        {
            "name": "ROS Local Graph",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/aqita_cloud/local_graph.py", // IMPORTANT: Change to your de"debugpy" node's path
            "console": "integratedTerminal",
            "justMyCode": false, // Set to false to step into ROS 2 libraries
            "envFile": "${workspaceFolder}/.env",
            "preLaunchTask": "SOURCE",
            "cwd": "${workspaceFolder}", // Set current working directory to workspace root,
            "python": "/usr/bin/python"
        },
    ]
}
