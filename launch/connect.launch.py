from launch import LaunchDescription
from launch_ros.actions import Node
from launch.substitutions import LaunchConfiguration
from launch.actions import DeclareLaunchArgument

def generate_launch_description():
    return LaunchDescription([
        # Declare launch arguments
        DeclareLaunchArgument('tenant', default_value='uvionix'),
        DeclareLaunchArgument('mqtt_root_ca', default_value='/workspace/drone_credentials/root-CA.crt'),
        DeclareLaunchArgument('mqtt_endpoint', default_value='a3i72wo3tqo4n3-ats.iot.eu-central-1.amazonaws.com'),
        DeclareLaunchArgument('mqtt_client_id', default_value='drone-1'),
        DeclareLaunchArgument('mqtt_client_certificate', default_value='/workspace/drone_credentials/certificate.pem'),
        DeclareLaunchArgument('mqtt_client_key', default_value='/workspace/drone_credentials/private.key'),
        DeclareLaunchArgument('processing_interval', default_value='0.5'),
        DeclareLaunchArgument('enable_server', default_value='false'),
        DeclareLaunchArgument('server_port', default_value='8000'),
        DeclareLaunchArgument('enable_archives', default_value='false'),
        DeclareLaunchArgument('archive_duration', default_value='600'),
        
        Node(
            package='aqita_cloud',
            executable='cloud_connector',
            name='cloud_connector',
            parameters=[{
                'tenant': LaunchConfiguration('tenant'),
                'mqtt_root_ca': LaunchConfiguration('mqtt_root_ca'),
                'mqtt_endpoint': LaunchConfiguration('mqtt_endpoint'),
                'mqtt_client_id': LaunchConfiguration('mqtt_client_id'),
                'mqtt_client_certificate': LaunchConfiguration('mqtt_client_certificate'),
                'mqtt_client_key': LaunchConfiguration('mqtt_client_key'),
                'processing_interval': LaunchConfiguration('processing_interval'),
                'enable_server': LaunchConfiguration('enable_server'),
                'enable_archives': LaunchConfiguration('enable_archives'),
                'archive_duration': LaunchConfiguration('archive_duration'),
            }],
            output='screen'
        )
    ])