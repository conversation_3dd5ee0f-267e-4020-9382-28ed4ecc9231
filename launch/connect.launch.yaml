launch:
- arg:
    name: "tenant"
    default: "uvionix"
- arg:
    name: "mqtt_root_ca"
    default: "/workspace/drone_credentials/root-CA.crt"
- arg:
    name: "mqtt_endpoint"
    default: "a3i72wo3tqo4n3-ats.iot.eu-central-1.amazonaws.com"
- arg:
    name: "mqtt_client_id"
    default: "drone-1"
- arg:
    name: "mqtt_client_certificate"
    default: "/workspace/drone_credentials/certificate.pem"
- arg:
    name: "mqtt_client_key"
    default: "/workspace/drone_credentials/private.key"
- arg:
    name: "processing_interval"
    default: "0.5"
- arg:
    name: "enable_server"
    default: "false"
- arg:
    name: "server_port"
    default: "8000"
- arg:
    name: "enable_archives"
    default: "false"
- arg:
    name: "archive_duration"
    default: "600"

  
- node:
    pkg: "aqita_cloud"
    exec: "cloud_connector"
    name: "cloud_connector"
    param:
    -
        name: "tenant"
        value: "$(var tenant)"
    -
        name: "mqtt_root_ca"
        value: "$(var mqtt_root_ca)"
    -
        name: "mqtt_endpoint"
        value: "$(var mqtt_endpoint)"
    -
        name: "mqtt_client_id"
        value: "$(var mqtt_client_id)"
    -
        name: "mqtt_client_certificate"
        value: "$(var mqtt_client_certificate)"
    -
        name: "mqtt_client_key"
        value: "$(var mqtt_client_key)"
    -
        name: "processing_interval"
        value: "$(var processing_interval)"
    -
        name: "enable_server"
        value: "$(var enable_server)"
    -
        name: "server_port"
        value: "$(var server_port)"
    -
        name: "enable_archives"
        value: "$(var enable_archives)"
    -
        name: "archive_duration"
        value: "$(var archive_duration)"