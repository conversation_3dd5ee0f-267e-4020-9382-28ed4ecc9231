HOW 2 DEBUG:

python -m venv venv
source venv/bin/activate
pip install -r requirements.txt



HOW 2 BUILD :

cd /workspace

rm -rf /workspace/build && rm -rf /workspace/install

colcon build --symlink-install --packages-select aqita_interfaces
colcon build --symlink-install --packages-select aqita_cloud

source /workspace/install/setup.bash

rosdep update

rosdep install --from-paths /workspace/aqita_cloud/package.xml --ignore-src -r -y

cd /workspace/aqita_cloud 

pip3 install -e .

cd /workspace

ros2 run aqita_cloud synthetic_server
ros2 run aqita_cloud esc_server


ros2 launch aqita_cloud simulation.launch.py max_messages:=1000 rate:=10
ros2 launch aqita_cloud connect.launch.py