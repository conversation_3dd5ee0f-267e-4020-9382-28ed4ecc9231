<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Logs</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', path='css/bootstrap-icons.css') }}">
    <style>
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        [data-bs-theme="dark"] {
            color-scheme: dark;
        }
        [data-bs-theme="dark"] .card {
            background-color: #2b3035;
            border-color: #373b3e;
        }
        [data-bs-theme="dark"] .card-body {
            color: #e9ecef;
        }
        [data-bs-theme="dark"] .table {
            color: #e9ecef;
        }
        [data-bs-theme="dark"] .table td,
        [data-bs-theme="dark"] .table th {
            border-color: #373b3e;
        }
        .log-table {
            font-family: monospace;
            font-size: 0.9em;
        }
        .log-entry {
            white-space: pre-wrap;
            word-break: break-word;
        }
        .log-level-DEBUG { color: #6c757d; }
        .log-level-INFO { color: #0d6efd; }
        .log-level-WARNING { color: #ffc107; }
        .log-level-ERROR { color: #dc3545; }
        .log-level-CRITICAL { color: #dc3545; font-weight: bold; }
        [data-bs-theme="dark"] .log-level-DEBUG { color: #adb5bd; }
        [data-bs-theme="dark"] .log-level-INFO { color: #6ea8fe; }
        [data-bs-theme="dark"] .log-level-WARNING { color: #ffc107; }
        [data-bs-theme="dark"] .log-level-ERROR { color: #ff6b6b; }
        [data-bs-theme="dark"] .log-level-CRITICAL { color: #ff6b6b; font-weight: bold; }
        .navbar {
            height: 3em;
            padding: 0;
            line-height: 3em;
        }
        .navbar-brand {
            font-size: 1.2em;
            line-height: 3em;
            padding: 0 1rem;
        }
        .navbar .container {
            height: 100%;
        }
        .navbar .btn {
            line-height: 2em;
            margin: 0.5em 0;
        }
        .main-container {
            height: calc(100vh - 3em);
            padding: 0.5rem;
            overflow: auto;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">Real-time Metrics</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/archive">Archive</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/logs">Logs</a>
                    </li>
                </ul>
                <div class="d-flex">
                    <button class="btn btn-outline-light ms-2" id="themeToggle">
                        <i class="bi bi-moon-fill"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <div class="main-container">
        <div class="container-fluid">
            <div class="row">
                <div class="col">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">System Logs</h5>
                            <div>
                                <button class="btn btn-sm btn-outline-secondary" onclick="clearLogs()">Clear</button>
                                <button class="btn btn-sm btn-outline-primary" onclick="scrollToBottom()">Scroll to Bottom</button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm log-table">
                                    <thead>
                                        <tr>
                                            <th>Timestamp</th>
                                            <th>Level</th>
                                            <th>Thread</th>
                                            <th>Message</th>
                                        </tr>
                                    </thead>
                                    <tbody id="logTableBody">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', path='js/bootstrap.bundle.min.js') }}"></script>
    <script>
        let eventSource;
        const logTableBody = document.getElementById('logTableBody');
        const maxLogs = 1000; // Maximum number of logs to keep in memory

        // Theme toggle functionality
        const themeToggle = document.getElementById('themeToggle');
        const html = document.documentElement;
        
        // Check for saved theme preference
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme) {
            html.setAttribute('data-bs-theme', savedTheme);
            updateThemeIcon(savedTheme);
        }
        
        themeToggle.addEventListener('click', () => {
            const currentTheme = html.getAttribute('data-bs-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            html.setAttribute('data-bs-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            updateThemeIcon(newTheme);
        });
        
        function updateThemeIcon(theme) {
            themeToggle.innerHTML = theme === 'dark' 
                ? '<i class="bi bi-sun-fill"></i>'
                : '<i class="bi bi-moon-fill"></i>';
        }

        function addLogEntry(log) {
            const row = document.createElement('tr');
            row.className = `log-level-${log.level}`;
            
            const timestampCell = document.createElement('td');
            timestampCell.textContent = new Date(log.timestamp).toLocaleString();
            
            const levelCell = document.createElement('td');
            levelCell.textContent = log.level;
            
            const threadCell = document.createElement('td');
            threadCell.textContent = log.thread;
            threadCell.style.fontFamily = 'monospace';
            threadCell.style.fontSize = '0.9em';
            
            const messageCell = document.createElement('td');
            messageCell.className = 'log-entry';
            messageCell.textContent = log.message;
            
            row.appendChild(timestampCell);
            row.appendChild(levelCell);
            row.appendChild(threadCell);
            row.appendChild(messageCell);
            
            logTableBody.insertBefore(row, logTableBody.firstChild);
            
            // Remove oldest log if we exceed maxLogs
            if (logTableBody.children.length > maxLogs) {
                logTableBody.removeChild(logTableBody.lastChild);
            }
        }

        function clearLogs() {
            logTableBody.innerHTML = '';
        }

        function scrollToBottom() {
            window.scrollTo(0, document.body.scrollHeight);
        }

        async function loadHistoricalLogs() {
            try {
                const response = await fetch('/log_historical');
                const logs = await response.json();
                logs.forEach(log => addLogEntry(log));
            } catch (error) {
                console.error('Error loading historical logs:', error);
            }
        }

        function startEventSource() {
            if (eventSource) {
                eventSource.close();
            }

            eventSource = new EventSource('/log_current');
            
            eventSource.addEventListener('log', (event) => {
                const log = JSON.parse(event.data);
                addLogEntry(log);
            });

            eventSource.addEventListener('error', (error) => {
                console.error('SSE Error:', error);
                // Attempt to reconnect after 5 seconds
                setTimeout(startEventSource, 5000);
            });
        }

        // Initialize
        loadHistoricalLogs();
        startEventSource();

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (eventSource) {
                eventSource.close();
            }
        });
    </script>
</body>
</html> 