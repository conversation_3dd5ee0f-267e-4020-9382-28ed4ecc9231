<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Real-time Metrics{% endblock %}</title>
    <link rel="stylesheet" href="{{ url_for('static', path='css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', path='css/bootstrap-icons.css') }}">
    <style>
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        [data-bs-theme="dark"] {
            color-scheme: dark;
        }
        [data-bs-theme="dark"] .card {
            background-color: #2b3035;
            border-color: #373b3e;
        }
        [data-bs-theme="dark"] .card-body {
            color: #e9ecef;
        }
        [data-bs-theme="dark"] .btn-outline-primary {
            color: #6ea8fe;
            border-color: #6ea8fe;
        }
        [data-bs-theme="dark"] .btn-outline-primary:hover {
            color: #fff;
            background-color: #6ea8fe;
        }
        [data-bs-theme="dark"] .btn-outline-primary.active {
            color: #fff;
            background-color: #6ea8fe;
        }
        [data-bs-theme="dark"] .btn-outline-secondary {
            color: #adb5bd;
            border-color: #adb5bd;
        }
        [data-bs-theme="dark"] .btn-outline-secondary:hover {
            color: #fff;
            background-color: #adb5bd;
        }
        [data-bs-theme="dark"] .form-range::-webkit-slider-thumb {
            background: #6ea8fe;
        }
        [data-bs-theme="dark"] .form-range::-moz-range-thumb {
            background: #6ea8fe;
        }
        [data-bs-theme="dark"] .form-range::-ms-thumb {
            background: #6ea8fe;
        }
        .navbar {
            height: 3em;
            padding: 0;
            line-height: 3em;
        }
        .navbar-brand {
            font-size: 1.2em;
            line-height: 3em;
            padding: 0 1rem;
        }
        .navbar .container {
            height: 100%;
        }
        .navbar .btn {
            line-height: 2em;
            margin: 0.5em 0;
        }
        .main-container {
            height: calc(100vh - 3em);
            padding: 0.5rem;
            overflow: auto;
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">Real-time Metrics</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link active" aria-current="page" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/archive">Archive</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/logs">Logs</a>
                    </li>
                </ul>
                <div class="d-flex">
                    <ul class="navbar-nav mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link" href="/backup_db" id="downloadDbNavbarButton">
                                <i class="bi bi-download"></i> Download DB
                            </a>
                        </li>
                    </ul>
                    <button class="btn btn-outline-light ms-2" id="themeToggle">
                        <i class="bi bi-moon-fill"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <div class="main-container">
        {% block content %}{% endblock %}
    </div>

    <script src="{{ url_for('static', path='js/bootstrap.bundle.min.js') }}"></script>
    <script>
        // Theme toggle functionality
        const themeToggle = document.getElementById('themeToggle');
        const html = document.documentElement;
        
        // Check for saved theme preference
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme) {
            html.setAttribute('data-bs-theme', savedTheme);
            updateThemeIcon(savedTheme);
        }
        
        themeToggle.addEventListener('click', () => {
            const currentTheme = html.getAttribute('data-bs-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            html.setAttribute('data-bs-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            updateThemeIcon(newTheme);
        });
        
        function updateThemeIcon(theme) {
            themeToggle.innerHTML = theme === 'dark' 
                ? '<i class="bi bi-sun-fill"></i>'
                : '<i class="bi bi-moon-fill"></i>';
        }
    </script>
    {% block extra_js %}{% endblock %}
</body>
</html> 